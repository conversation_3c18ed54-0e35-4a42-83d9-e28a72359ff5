'use client'

import Link from 'next/link'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

export default function Navigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const router = useRouter()
  const { user, loading, signOut } = useAuth()

  // Debug logging
  console.log('🔍 Navigation: User:', user, 'Loading:', loading)

  const handleLogout = async () => {
    try {
      await signOut()
      router.push('/')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  return (
    <nav className="fixed top-0 w-full z-50 bg-cream-50/95 backdrop-blur-sm border-b-2 border-navy-200 shadow-md">
      <div className="max-w-6xl mx-auto px-6 sm:px-8 lg:px-12">
        <div className="flex items-center justify-between py-6">
          <div className="flex items-center">
            <Link href="/" className="text-3xl font-display font-semibold text-burgundy-800 tracking-wide relative">
              <span className="text-gold-600 mr-2">❦</span>
              Magic Academy
              <div className="absolute -bottom-1 left-8 right-0 h-0.5 bg-gradient-to-r from-burgundy-600 to-transparent opacity-60"></div>
            </Link>
          </div>
          
          <div className="hidden md:flex items-center space-x-10">
            <Link href="/" className="text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group">
              Home
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full"></span>
            </Link>
            <Link href="/portfolio" className="text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group">
              Portfolio
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full"></span>
            </Link>
            <Link href="/blog" className="text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group">
              Blog
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full"></span>
            </Link>
            <Link href="/tutorials" className="text-navy-700 hover:text-burgundy-700 transition-all duration-300 font-display tracking-wide relative group">
              Tutorials
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-burgundy-700 transition-all duration-300 group-hover:w-full"></span>
            </Link>

            {/* 根据用户状态显示不同的导航选项 */}
            {loading ? (
              <div className="ml-4 w-16 h-8 bg-navy-200 rounded animate-pulse"></div>
            ) : user ? (
              <div className="flex items-center ml-4">
                <div className="relative group">
                  <button className="flex items-center text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide">
                    <span className="mr-1">{user.name || user.email.split('@')[0]}</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                  <div className="absolute right-0 top-full mt-2 w-48 bg-cream-50 border border-navy-200 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <div className="py-2">
                      <Link href="/library" className="block px-4 py-2 text-navy-700 hover:bg-cream-100 transition-colors font-body">
                        My Library
                      </Link>
                      <button
                        onClick={handleLogout}
                        className="block w-full text-left px-4 py-2 text-navy-700 hover:bg-cream-100 transition-colors font-body"
                      >
                        Sign Out
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <Link href="/auth/login" className="border border-gold-600 text-gold-700 hover:bg-gold-600 hover:text-cream-50 font-medium py-2 px-4 shadow-sm hover:shadow-md transition-all duration-300 font-display tracking-wide rounded ml-4 text-sm">Login</Link>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button 
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="text-navy-700 hover:text-burgundy-700 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden pb-6">
            <div className="flex flex-col space-y-4">
              <Link href="/" className="text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide">
                Home
              </Link>
              <Link href="/portfolio" className="text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide">
                Portfolio
              </Link>
              <Link href="/blog" className="text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide">
                Blog
              </Link>
              <Link href="/tutorials" className="text-navy-700 hover:text-burgundy-700 transition-colors font-display tracking-wide">
                Tutorials
              </Link>

              {/* 移动端用户状态 */}
              {loading ? (
                <div className="w-full h-8 bg-navy-200 rounded animate-pulse"></div>
              ) : user ? (
                <div className="pt-2 border-t border-navy-200">
                  <div className="text-navy-600 font-body text-sm mb-2">
                    Signed in as {user.name || user.email.split('@')[0]}
                  </div>
                  <Link href="/library" className="block text-navy-700 hover:text-burgundy-700 transition-colors font-body text-sm mb-2">
                    My Library
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="text-burgundy-600 hover:text-burgundy-800 transition-colors font-body text-sm"
                  >
                    Sign Out
                  </button>
                </div>
              ) : (
                <Link href="/auth/login" className="border border-gold-600 text-gold-700 hover:bg-gold-600 hover:text-cream-50 font-medium py-2 px-4 shadow-sm hover:shadow-md transition-all duration-300 font-display tracking-wide rounded inline-block text-center text-sm">
                  Login
                </Link>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
