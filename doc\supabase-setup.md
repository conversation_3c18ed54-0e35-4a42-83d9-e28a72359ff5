# Supabase 网页端配置指南

本文档详细说明如何在 Supabase 网页端完成项目配置，为个人魔术网站项目做好准备。

## 1. 创建 Supabase 项目

### 1.1 注册和登录
1. 访问 [https://supabase.com](https://supabase.com)
2. 点击 "Start your project" 或 "Sign up"
3. 使用 GitHub、Google 或邮箱注册账号
4. 登录到 Supabase Dashboard

### 1.2 创建新项目
1. 在 Dashboard 中点击 "New project"
2. 选择或创建一个 Organization（组织）
3. 填写项目信息：
   - **Name**: `magic-website` (或你喜欢的名称)
   - **Database Password**: 设置一个强密码（请妥善保存）
   - **Region**: 选择离你最近的区域（如 Singapore 或 Tokyo）
   - **Pricing Plan**: 选择 "Free" 计划
4. 点击 "Create new project"
5. 等待项目初始化完成（通常需要 1-2 分钟）

## 2. 获取项目配置信息

### 2.1 获取 API 密钥
1. 项目创建完成后，进入项目 Dashboard
2. 点击左侧菜单的 "Settings"（设置图标）
3. 选择 "API" 选项卡
4. 复制以下信息：
   - **Project URL**: `https://your-project-id.supabase.co`
   - **anon public key**: `eyJ...` (很长的字符串)
   - **service_role key**: `eyJ...` (保密，仅服务端使用)

### 2.2 保存配置信息
将获取的信息保存到项目的 `.env.local` 文件中：
```env
NEXT_PUBLIC_SUPABASE_URL="https://your-project-id.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-anon-public-key"
SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"
```

## 3. 配置数据库

### 3.1 创建数据表
1. 在项目 Dashboard 中，点击左侧菜单的 "SQL Editor"
2. 点击 "New query" 创建新的 SQL 查询
3. 复制并粘贴以下 SQL 代码：

```sql
-- 用户扩展信息表
CREATE TABLE profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE,
  email TEXT,
  name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (id)
);

-- 教程表
CREATE TABLE tutorials (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  cover_image TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 视频表
CREATE TABLE videos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  duration INTEGER NOT NULL,
  video_url TEXT NOT NULL,
  thumbnail_url TEXT,
  order_index INTEGER NOT NULL,
  tutorial_id UUID REFERENCES tutorials(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 购买记录表
CREATE TABLE purchases (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tutorial_id UUID REFERENCES tutorials(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  payment_id TEXT NOT NULL,
  status TEXT DEFAULT 'completed',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 启用行级安全策略 (RLS)
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE tutorials ENABLE ROW LEVEL SECURITY;
ALTER TABLE videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchases ENABLE ROW LEVEL SECURITY;
```

4. 点击 "Run" 执行 SQL 语句

### 3.2 设置安全策略 (RLS Policies)
1. 继续在 SQL Editor 中创建新查询
2. 执行以下 SQL 来设置数据访问权限：

```sql
-- 用户 Profile 策略
CREATE POLICY "Users can view their own profile" 
ON profiles FOR SELECT 
USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" 
ON profiles FOR UPDATE 
USING (auth.uid() = id);

-- 教程访问策略（所有人可查看）
CREATE POLICY "Anyone can view tutorials" 
ON tutorials FOR SELECT 
TO authenticated, anon 
USING (true);

-- 视频访问策略（所有人可查看）
CREATE POLICY "Anyone can view videos" 
ON videos FOR SELECT 
TO authenticated, anon 
USING (true);

-- 购买记录策略（用户只能查看自己的购买记录）
CREATE POLICY "Users can view their own purchases" 
ON purchases FOR SELECT 
USING (auth.uid() = user_id);
```

### 3.3 创建自动触发器
创建用户注册时自动创建 profile 的触发器：

```sql
-- 创建处理新用户的函数
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, email, name)
  VALUES (
    new.id, 
    new.email, 
    COALESCE(new.raw_user_meta_data->>'name', split_part(new.email, '@', 1))
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建触发器
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
```

## 4. 配置认证设置

### 4.1 邮箱认证设置
1. 进入 "Authentication" → "Settings"
2. 在 "User Signups" 部分：
   - 确保 "Enable email confirmations" 已启用
   - 设置 "Site URL" 为你的网站地址（开发时使用 `http://localhost:3000`）
3. 在 "Email Templates" 中可以自定义邮件模板

### 4.2 URL 配置
在 "Authentication" → "URL Configuration" 中设置：
- **Site URL**: `http://localhost:3000` (开发环境)
- **Redirect URLs**: 添加 `http://localhost:3000/auth/callback`

## 5. 测试数据库连接

### 5.1 插入测试数据
在 SQL Editor 中插入一些测试数据：

```sql
-- 插入测试教程
INSERT INTO tutorials (title, description, price, cover_image) VALUES
('基础纸牌魔术', '学习基本的纸牌手法和技巧', 29.99, '/images/tutorial1.jpg'),
('高级心灵魔术', '掌握读心术的秘密', 49.99, '/images/tutorial2.jpg');

-- 获取教程ID（用于插入视频）
-- 先查看插入的教程
SELECT id, title FROM tutorials;
```

### 5.2 验证表结构
在 "Table Editor" 中查看创建的表：
1. 点击左侧菜单的 "Table Editor"
2. 确认可以看到 `profiles`、`tutorials`、`videos`、`purchases` 四个表
3. 点击各个表查看结构和数据

## 6. 监控和日志

### 6.1 查看实时日志
1. 进入 "Logs" 页面
2. 可以查看 API 请求、认证事件、数据库查询等日志
3. 这对调试很有帮助

### 6.2 API 使用情况
在 "Settings" → "Usage" 中可以查看：
- 数据库大小
- API 请求次数
- 带宽使用情况
- 认证用户数量

## 7. 备份和恢复

### 7.1 数据库备份
1. 进入 "Settings" → "Database"
2. 在 "Database backups" 部分可以：
   - 查看自动备份（每日备份，保留 7 天）
   - 手动创建备份
   - 下载备份文件

## 8. 常见问题

### 8.1 连接失败
- 检查 API URL 和密钥是否正确
- 确认网络连接正常
- 查看 Logs 页面的错误信息

### 8.2 认证问题
- 确认邮箱确认功能已启用
- 检查 Site URL 配置
- 查看 Authentication 日志

### 8.3 权限问题
- 检查 RLS 策略是否正确设置
- 确认用户角色和权限
- 在 SQL Editor 中测试查询

## 9. 生产环境配置

当准备部署到生产环境时：

1. **更新 URL 配置**：
   - Site URL 改为生产域名
   - 添加生产环境的 Redirect URLs

2. **安全设置**：
   - 启用额外的安全功能
   - 配置 CORS 设置
   - 设置 API 速率限制

3. **监控告警**：
   - 设置使用量告警
   - 配置错误通知

## 10. 下一步

完成 Supabase 配置后，你可以：
1. 在代码中集成 Supabase 客户端
2. 实现用户注册和登录功能
3. 连接教程和视频数据
4. 集成支付系统
5. 配置视频托管服务

---

**注意**: 请妥善保管你的数据库密码和 service_role key，不要将其提交到代码仓库中。