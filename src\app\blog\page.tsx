import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { BlogPost } from '@/types'
import { BlogDataAccess } from '@/lib/data-access'

async function getBlogPosts(): Promise<BlogPost[]> {
  try {
    return await BlogDataAccess.getAll()
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    return []
  }
}
export default async function BlogPage() {
  const blogPosts = await getBlogPosts()
  return (
    <div className="min-h-screen bg-cream-50 text-navy-900">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-6xl text-burgundy-700 rotate-12">❦</div>
          <div className="absolute top-20 right-20 text-4xl text-gold-600 -rotate-12">◆</div>
          <div className="absolute bottom-20 left-20 text-5xl text-navy-600 rotate-45">✦</div>
          <div className="absolute bottom-10 right-10 text-3xl text-burgundy-600 -rotate-45">❦</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="classical-border mb-12">
            <h1 className="text-5xl md:text-6xl font-display font-semibold mb-6 text-navy-900 text-shadow">
              Magic Insights
            </h1>
            <p className="text-xl md:text-2xl text-navy-700 max-w-3xl mx-auto font-body leading-relaxed">
              Deep thoughts on the art, theory, and philosophy of magic, exploring the timeless principles 
              that govern our craft and the profound connections between performer and audience.
            </p>
          </div>
        </div>
      </section>

      {/* Featured Article */}
      {blogPosts.length > 0 && (
        <section className="pb-16">
          <div className="container-max">
            <div className="classical-border mb-12 text-center">
              <h2 className="text-3xl md:text-4xl font-display font-medium text-navy-900 mb-4">
                Featured Article
              </h2>
            </div>
            
            <Link href={`/blog/${blogPosts[0].slug}`} className="group block">
              <div className="card hover-lift group-hover:shadow-2xl transition-all duration-300 max-w-4xl mx-auto">
                <div className="text-center mb-6">
                  <div className="w-16 h-16 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-burgundy-800 transition-colors">
                    <svg className="w-6 h-6 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  </div>
                </div>
                
                <h3 className="text-3xl md:text-4xl font-display font-medium mb-4 text-burgundy-700 group-hover:text-burgundy-800 transition-colors text-center">
                  {blogPosts[0].title}
                </h3>
                
                <div className="flex items-center justify-center space-x-4 text-navy-600 font-body mb-6">
                  <span className="flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {blogPosts[0].publishedAt?.toLocaleDateString('en-US', { 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric' 
                    })}
                  </span>
                  <span className="flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    5 min read
                  </span>
                </div>
                
                <p className="text-lg text-navy-700 font-body leading-relaxed mb-6 text-center max-w-3xl mx-auto">
                  {blogPosts[0].excerpt}
                </p>
                
                <div className="text-center">
                  <span className="text-burgundy-600 font-display text-lg group-hover:text-burgundy-800 transition-colors">
                    Read Full Article →
                  </span>
                </div>
              </div>
            </Link>
          </div>
        </section>
      )}

      {/* All Articles */}
      <section className="section-padding bg-cream-100 paper-texture">
        <div className="container-max">
          <div className="classical-border mb-12 text-center">
            <h2 className="text-3xl md:text-4xl font-display font-medium text-navy-900 mb-4">
              All Articles
            </h2>
            <p className="text-navy-600 font-body text-lg max-w-2xl mx-auto">
              Explore our complete collection of magical insights and philosophical reflections
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 lg:gap-10">
            {blogPosts.slice(1).map((post) => (
              <Link 
                key={post.id} 
                href={`/blog/${post.slug}`}
                className="group block"
              >
                <article className="card hover-lift group-hover:shadow-2xl transition-all duration-300 h-full">
                  <div className="flex flex-col h-full">
                    <div className="mb-4">
                      <div className="w-12 h-12 bg-gold-600 rounded-full flex items-center justify-center mb-4 group-hover:bg-gold-700 transition-colors">
                        <svg className="w-5 h-5 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                    </div>
                    
                    <h3 className="text-xl md:text-2xl font-display font-medium mb-3 text-burgundy-700 group-hover:text-burgundy-800 transition-colors">
                      {post.title}
                    </h3>
                    
                    <div className="flex items-center space-x-4 text-navy-600 font-body text-sm mb-4">
                      <span className="flex items-center">
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {post.publishedAt?.toLocaleDateString('en-US', { 
                          year: 'numeric', 
                          month: 'short', 
                          day: 'numeric' 
                        })}
                      </span>
                      <span className="flex items-center">
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        4 min read
                      </span>
                    </div>
                    
                    <p className="text-navy-700 font-body leading-relaxed mb-4 flex-grow">
                      {post.excerpt}
                    </p>
                    
                    <div className="pt-4 border-t border-navy-200">
                      <span className="text-burgundy-600 font-display text-sm group-hover:text-burgundy-800 transition-colors">
                        Continue Reading →
                      </span>
                    </div>
                  </div>
                </article>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="section-padding">
        <div className="container-max">
          <div className="max-w-2xl mx-auto text-center">
            <div className="ornamental-divider mb-8"></div>
            <h2 className="text-3xl md:text-4xl font-display font-medium mb-6 text-navy-900">
              Stay Connected
            </h2>
            <p className="text-lg text-navy-700 mb-8 font-body leading-relaxed">
              Subscribe to receive new articles and insights directly in your inbox. 
              Join our community of thoughtful magical practitioners.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input 
                type="email" 
                placeholder="Enter your email"
                className="input-field flex-grow"
              />
              <button className="btn-primary">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Classical Footer */}
      <footer className="section-padding bg-navy-900 border-t-4 border-gold-600 relative">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-4xl text-gold-400">❦</div>
          <div className="absolute top-10 right-10 text-4xl text-gold-400 rotate-180">❦</div>
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200">◆</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="section-divider mb-12"></div>
          <div className="flex items-center justify-center mb-6">
            <span className="text-gold-400 text-2xl mr-3">❦</span>
            <div className="text-3xl font-display font-semibold text-cream-100 tracking-wide">
              Magic Academy
            </div>
            <span className="text-gold-400 text-2xl ml-3 rotate-180">❦</span>
          </div>
          <p className="text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic">
            "Preserving the classical traditions of magical artistry for future generations,
            where timeless elegance meets the wonder of the impossible."
          </p>
          <div className="flex flex-wrap justify-center gap-4 sm:gap-8 mb-8">
            <Link href="/" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Home</Link>
            <Link href="/portfolio" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Portfolio</Link>
            <Link href="/blog" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Blog</Link>
            <Link href="/tutorials" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Tutorials</Link>
          </div>
          <div className="border-t border-navy-700 pt-8">
            <p className="text-navy-400 font-body">
              © 2025 Magic Academy. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
