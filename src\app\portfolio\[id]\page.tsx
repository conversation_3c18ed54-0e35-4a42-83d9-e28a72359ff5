import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { PortfolioItem } from '@/types'
import { notFound } from 'next/navigation'
import { PortfolioDataAccess } from '@/lib/data-access'

async function getPortfolioItem(id: string): Promise<PortfolioItem | null> {
  try {
    return await PortfolioDataAccess.getById(id)
  } catch (error) {
    console.error('Error fetching portfolio item:', error)
    return null
  }
}

async function getAllPortfolioItems(): Promise<PortfolioItem[]> {
  try {
    return await PortfolioDataAccess.getAll()
  } catch (error) {
    console.error('Error fetching portfolio items:', error)
    return []
  }
}
interface PortfolioDetailPageProps {
  params: {
    id: string
  }
}

export default async function PortfolioDetailPage({ params }: PortfolioDetailPageProps) {
  const [item, portfolioItems] = await Promise.all([
    getPortfolioItem(params.id),
    getAllPortfolioItems()
  ])

  if (!item) {
    notFound()
  }

  return (
    <div className="min-h-screen bg-cream-50 text-navy-900">
      <Navigation />
      
      {/* Breadcrumb */}
      <section className="pt-32 pb-8">
        <div className="container-max">
          <nav className="flex items-center space-x-2 text-sm font-body">
            <Link href="/" className="text-navy-600 hover:text-burgundy-700 transition-colors">
              Home
            </Link>
            <span className="text-navy-400">→</span>
            <Link href="/portfolio" className="text-navy-600 hover:text-burgundy-700 transition-colors">
              Portfolio
            </Link>
            <span className="text-navy-400">→</span>
            <span className="text-burgundy-700 font-medium">{item.title}</span>
          </nav>
        </div>
      </section>

      {/* Main Content */}
      <section className="pb-16">
        <div className="container-max">
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16">
            {/* Media Section */}
            <div className="order-2 lg:order-1">
              <div className="sticky top-32">
                <div className="relative overflow-hidden rounded-lg shadow-2xl bg-navy-100">
                  <div className="aspect-video bg-gradient-to-br from-navy-200 to-burgundy-100 flex items-center justify-center">
                    {item.mediaType === 'video' ? (
                      <div className="text-center">
                        <div className="w-20 h-20 bg-burgundy-700 rounded-full flex items-center justify-center mb-4 mx-auto hover:bg-burgundy-800 transition-colors cursor-pointer">
                          <svg className="w-8 h-8 text-cream-50 ml-1" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8 5v14l11-7z"/>
                          </svg>
                        </div>
                        <p className="text-navy-600 font-display">Click to Play Video</p>
                        <p className="text-navy-500 font-body text-sm mt-1">High-quality performance recording</p>
                      </div>
                    ) : (
                      <div className="text-center">
                        <div className="w-20 h-20 bg-gold-600 rounded-full flex items-center justify-center mb-4 mx-auto">
                          <svg className="w-8 h-8 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                        </div>
                        <p className="text-navy-600 font-display">Performance Gallery</p>
                        <p className="text-navy-500 font-body text-sm mt-1">Professional photography</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Content Section */}
            <div className="order-1 lg:order-2">
              <div className="classical-border mb-8">
                <h1 className="text-4xl md:text-5xl font-display font-semibold mb-4 text-navy-900 text-shadow">
                  {item.title}
                </h1>
                <div className="flex items-center space-x-4 text-navy-600 font-body">
                  <span className="flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {item.createdAt.toLocaleDateString('en-US', { 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric' 
                    })}
                  </span>
                  <span className="flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    {item.mediaType === 'video' ? 'Video Performance' : 'Image Gallery'}
                  </span>
                </div>
              </div>

              <div className="prose prose-lg max-w-none">
                <p className="text-xl text-navy-700 font-body leading-relaxed mb-8">
                  {item.description}
                </p>
              </div>

              <div className="ornamental-divider my-8"></div>

              {/* Performance Details */}
              <div className="card mb-8">
                <h3 className="text-2xl font-display font-medium mb-4 text-burgundy-700">
                  Performance Details
                </h3>
                <div className="space-y-4 text-navy-700 font-body">
                  <div className="flex justify-between items-center py-2 border-b border-navy-200">
                    <span className="font-medium">Performance Type:</span>
                    <span>{item.mediaType === 'video' ? 'Live Performance' : 'Studio Photography'}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-navy-200">
                    <span className="font-medium">Skill Level:</span>
                    <span>Advanced</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-navy-200">
                    <span className="font-medium">Category:</span>
                    <span>Classical Magic</span>
                  </div>
                  <div className="flex justify-between items-center py-2">
                    <span className="font-medium">Duration:</span>
                    <span>{item.mediaType === 'video' ? '5-8 minutes' : 'Multiple angles'}</span>
                  </div>
                </div>
              </div>

              {/* Call to Action */}
              <div className="bg-cream-100 border-2 border-gold-200 rounded-lg p-6 text-center">
                <h4 className="text-xl font-display font-medium mb-3 text-burgundy-700">
                  Want to Learn This Technique?
                </h4>
                <p className="text-navy-700 font-body mb-4">
                  Discover the secrets behind this mesmerizing performance in our exclusive tutorial collection.
                </p>
                <Link href="/tutorials" className="btn-primary">
                  Explore Tutorials
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Related Portfolio Items */}
      <section className="section-padding bg-cream-100 paper-texture">
        <div className="container-max">
          <div className="classical-border mb-12 text-center">
            <h2 className="text-3xl md:text-4xl font-display font-medium text-navy-900 mb-4">
              More Performances
            </h2>
            <p className="text-navy-600 font-body text-lg max-w-2xl mx-auto">
              Explore other captivating performances from our portfolio
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {portfolioItems
              .filter(relatedItem => relatedItem.id !== item.id)
              .slice(0, 3)
              .map((relatedItem) => (
                <Link 
                  key={relatedItem.id} 
                  href={`/portfolio/${relatedItem.id}`}
                  className="group block"
                >
                  <div className="card hover-lift group-hover:shadow-2xl transition-all duration-300">
                    <div className="relative mb-4 overflow-hidden rounded-lg bg-navy-100">
                      <div className="aspect-video bg-gradient-to-br from-navy-200 to-burgundy-100 flex items-center justify-center">
                        {relatedItem.mediaType === 'video' ? (
                          <div className="w-12 h-12 bg-burgundy-700 rounded-full flex items-center justify-center group-hover:bg-burgundy-800 transition-colors">
                            <svg className="w-4 h-4 text-cream-50 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M8 5v14l11-7z"/>
                            </svg>
                          </div>
                        ) : (
                          <div className="w-12 h-12 bg-gold-600 rounded-full flex items-center justify-center group-hover:bg-gold-700 transition-colors">
                            <svg className="w-4 h-4 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                        )}
                      </div>
                    </div>
                    <h3 className="text-lg font-display font-medium mb-2 text-burgundy-700 group-hover:text-burgundy-800 transition-colors">
                      {relatedItem.title}
                    </h3>
                    <p className="text-navy-700 font-body text-sm leading-relaxed">
                      {relatedItem.description.substring(0, 100)}...
                    </p>
                  </div>
                </Link>
              ))}
          </div>
        </div>
      </section>

      {/* Classical Footer */}
      <footer className="section-padding bg-navy-900 border-t-4 border-gold-600 relative">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-4xl text-gold-400">❦</div>
          <div className="absolute top-10 right-10 text-4xl text-gold-400 rotate-180">❦</div>
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200">◆</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="section-divider mb-12"></div>
          <div className="flex items-center justify-center mb-6">
            <span className="text-gold-400 text-2xl mr-3">❦</span>
            <div className="text-3xl font-display font-semibold text-cream-100 tracking-wide">
              Magic Academy
            </div>
            <span className="text-gold-400 text-2xl ml-3 rotate-180">❦</span>
          </div>
          <p className="text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic">
            "Preserving the classical traditions of magical artistry for future generations,
            where timeless elegance meets the wonder of the impossible."
          </p>
          <div className="flex flex-wrap justify-center gap-4 sm:gap-8 mb-8">
            <Link href="/" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Home</Link>
            <Link href="/portfolio" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Portfolio</Link>
            <Link href="/blog" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Blog</Link>
            <Link href="/tutorials" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Tutorials</Link>
          </div>
          <div className="border-t border-navy-700 pt-8">
            <p className="text-navy-400 font-body">
              © 2025 Magic Academy. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
