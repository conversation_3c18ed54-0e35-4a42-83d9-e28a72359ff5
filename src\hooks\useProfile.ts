'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import type { AppUser } from '@/types'

interface UseProfileReturn {
  profile: AppUser | null
  loading: boolean
  error: string | null
  updateProfile: (updates: Partial<AppUser>) => Promise<boolean>
  refetch: () => Promise<void>
}

/**
 * Hook for managing user profile with direct Supabase queries
 * Replaces the API route approach with direct database access
 */
export function useProfile(userId?: string): UseProfileReturn {
  const [profile, setProfile] = useState<AppUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchProfile = async () => {
    if (!userId) {
      setProfile(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      const { data, error: queryError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (queryError) {
        // If profile doesn't exist, this might be a new user
        if (queryError.code === 'PGRST116') {
          // Try to get user info from auth.users
          const { data: { user }, error: authError } = await supabase.auth.getUser()
          
          if (authError || !user || user.id !== userId) {
            throw new Error('User not found')
          }

          // Create a basic profile for the user
          const newProfile = {
            id: user.id,
            email: user.email!,
            name: user.user_metadata?.name || user.email!.split('@')[0],
            created_at: user.created_at,
            updated_at: user.updated_at || user.created_at
          }

          const { data: createdProfile, error: createError } = await supabase
            .from('profiles')
            .insert(newProfile)
            .select()
            .single()

          if (createError) {
            throw new Error(`Failed to create profile: ${createError.message}`)
          }

          setProfile({
            id: createdProfile.id,
            email: createdProfile.email,
            name: createdProfile.name,
            createdAt: new Date(createdProfile.created_at),
            updatedAt: new Date(createdProfile.updated_at)
          })
        } else {
          throw new Error(`Failed to fetch profile: ${queryError.message}`)
        }
      } else {
        setProfile({
          id: data.id,
          email: data.email,
          name: data.name,
          createdAt: new Date(data.created_at),
          updatedAt: new Date(data.updated_at)
        })
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch profile'
      setError(errorMessage)
      console.error('useProfile error:', err)
    } finally {
      setLoading(false)
    }
  }

  const updateProfile = async (updates: Partial<AppUser>): Promise<boolean> => {
    if (!userId) {
      setError('User ID is required for profile updates')
      return false
    }

    try {
      setError(null)

      // Prepare the update data
      const updateData: any = {
        updated_at: new Date().toISOString()
      }

      if (updates.name !== undefined) {
        updateData.name = updates.name
      }
      if (updates.email !== undefined) {
        updateData.email = updates.email
      }

      const { data, error: updateError } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single()

      if (updateError) {
        throw new Error(`Failed to update profile: ${updateError.message}`)
      }

      // Update local state
      setProfile({
        id: data.id,
        email: data.email,
        name: data.name,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at)
      })

      return true
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update profile'
      setError(errorMessage)
      console.error('useProfile updateProfile error:', err)
      return false
    }
  }

  const refetch = async () => {
    await fetchProfile()
  }

  useEffect(() => {
    fetchProfile()
  }, [userId])

  return {
    profile,
    loading,
    error,
    updateProfile,
    refetch
  }
}

/**
 * Hook for getting current user profile from auth session
 * Combines auth user data with profile data
 */
export function useCurrentUserProfile() {
  const [profile, setProfile] = useState<AppUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function getCurrentProfile() {
      try {
        setLoading(true)
        setError(null)

        // Get current user from auth
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError || !user) {
          setProfile(null)
          setLoading(false)
          return
        }

        // Get profile data
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single()

        if (profileError) {
          // If no profile exists, create one
          if (profileError.code === 'PGRST116') {
            const newProfile = {
              id: user.id,
              email: user.email!,
              name: user.user_metadata?.name || user.email!.split('@')[0],
              created_at: user.created_at,
              updated_at: user.updated_at || user.created_at
            }

            const { data: createdProfile, error: createError } = await supabase
              .from('profiles')
              .insert(newProfile)
              .select()
              .single()

            if (createError) {
              throw new Error(`Failed to create profile: ${createError.message}`)
            }

            setProfile({
              id: createdProfile.id,
              email: createdProfile.email,
              name: createdProfile.name,
              createdAt: new Date(createdProfile.created_at),
              updatedAt: new Date(createdProfile.updated_at)
            })
          } else {
            throw profileError
          }
        } else {
          setProfile({
            id: profileData.id,
            email: profileData.email,
            name: profileData.name,
            createdAt: new Date(profileData.created_at),
            updatedAt: new Date(profileData.updated_at)
          })
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to get current user profile'
        setError(errorMessage)
        console.error('useCurrentUserProfile error:', err)
        setProfile(null)
      } finally {
        setLoading(false)
      }
    }

    getCurrentProfile()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        getCurrentProfile()
      } else if (event === 'SIGNED_OUT') {
        setProfile(null)
        setLoading(false)
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  return { profile, loading, error }
}
