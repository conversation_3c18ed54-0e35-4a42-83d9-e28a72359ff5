import Navigation from '@/components/Navigation'
import Link from 'next/link'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-cream-50 text-navy-900">
      <Navigation />

      {/* Classical Hero Section */}
      <section id="home" className="hero-section pt-24 relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-6xl text-burgundy-700 rotate-12">❦</div>
          <div className="absolute top-20 right-20 text-4xl text-gold-600 -rotate-12">◆</div>
          <div className="absolute bottom-20 left-20 text-5xl text-navy-600 rotate-45">✦</div>
          <div className="absolute bottom-10 right-10 text-3xl text-burgundy-600 -rotate-45">❦</div>
        </div>

        <div className="container-max text-center relative z-10">
          {/* Decorative top border */}
          <div className="classical-border mb-12"></div>

          <h1 className="text-5xl md:text-7xl font-display font-semibold mb-8 text-navy-900 text-shadow">
            Master the Art of{' '}
            <span className="text-burgundy-700 italic relative">
              Magic
              <div className="absolute -bottom-2 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-gold-500 to-transparent"></div>
            </span>
          </h1>

          <div className="ornamental-divider"></div>

          <p className="text-xl md:text-2xl text-navy-700 mb-12 max-w-4xl mx-auto font-body leading-relaxed">
            Discover exclusive tutorials, original techniques, and professional insights
            from a world-class magician. Elevate your craft with premium educational content
            steeped in classical tradition and timeless elegance.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
            <Link href="/tutorials" className="btn-primary text-lg">
              Explore Tutorials
            </Link>
            <Link href="/portfolio" className="btn-outline text-lg">
              Watch Portfolio
            </Link>
          </div>

          {/* Classical quote */}
          <div className="max-w-2xl mx-auto">
            <blockquote className="text-lg italic text-navy-600 font-display border-l-4 border-gold-500 pl-6">
              "Magic is not about tricks, but about the artistry of wonder and the preservation of mystery."
            </blockquote>
          </div>
        </div>
      </section>

      {/* Classical Featured Content */}
      <section className="section-padding bg-cream-100 paper-texture">
        <div className="container-max">
          <div className="classical-border mb-16">
            <h2 className="text-4xl md:text-5xl font-display font-medium text-center text-navy-900 mb-4">
              Featured Content
            </h2>
            <p className="text-center text-navy-600 font-body text-lg max-w-2xl mx-auto">
              Explore our carefully curated collection of magical artistry and educational excellence
            </p>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
            <div className="card hover-lift flex flex-col h-full">
              <h3 className="text-2xl font-display font-medium mb-4 text-burgundy-700">Latest Portfolio</h3>
              <p className="text-navy-700 mb-6 font-body leading-relaxed flex-grow">
                Witness stunning performances and innovative techniques in action,
                showcasing the artistry and precision of classical magic.
              </p>
              <Link href="/portfolio" className="btn-secondary mt-auto">View Portfolio</Link>
            </div>

            <div className="card hover-lift flex flex-col h-full">
              <h3 className="text-2xl font-display font-medium mb-4 text-burgundy-700">Magic Insights</h3>
              <p className="text-navy-700 mb-6 font-body leading-relaxed flex-grow">
                Deep thoughts on the art, theory, and philosophy of magic,
                exploring the timeless principles that govern our craft.
              </p>
              <Link href="/blog" className="btn-secondary mt-auto">Read Blog</Link>
            </div>

            <div className="card hover-lift flex flex-col h-full">
              <h3 className="text-2xl font-display font-medium mb-4 text-burgundy-700">Premium Tutorials</h3>
              <p className="text-navy-700 mb-6 font-body leading-relaxed flex-grow">
                Exclusive educational content for serious magic practitioners,
                preserving and teaching the classical methods of the masters.
              </p>
              <Link href="/tutorials" className="btn-primary mt-auto">Start Learning</Link>
            </div>
          </div>
        </div>
      </section>

      {/* Classical Footer */}
      <footer className="section-padding bg-navy-900 border-t-4 border-gold-600 relative">
        {/* Decorative background elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-4xl text-gold-400">❦</div>
          <div className="absolute top-10 right-10 text-4xl text-gold-400 rotate-180">❦</div>
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200">◆</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="section-divider mb-12"></div>

          <div className="flex items-center justify-center mb-6">
            <span className="text-gold-400 text-2xl mr-3">❦</span>
            <div className="text-3xl font-display font-semibold text-cream-100 tracking-wide">
              Magic Academy
            </div>
            <span className="text-gold-400 text-2xl ml-3 rotate-180">❦</span>
          </div>

          <p className="text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic">
            "Preserving the classical traditions of magical artistry for future generations,
            where timeless elegance meets the wonder of the impossible."
          </p>

          {/* Navigation links */}
          <div className="flex flex-wrap justify-center gap-4 sm:gap-8 mb-8">
            <Link href="/" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Home</Link>
            <Link href="/portfolio" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Portfolio</Link>
            <Link href="/blog" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Blog</Link>
            <Link href="/tutorials" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Tutorials</Link>
          </div>

          <div className="border-t border-navy-700 pt-8">
            <p className="text-navy-400 font-body">
              © 2025 Magic Academy. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
