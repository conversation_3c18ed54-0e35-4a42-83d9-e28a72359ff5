'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import type { Purchase, Tutorial } from '@/types'

interface UsePurchasesReturn {
  purchases: Purchase[]
  purchasedTutorials: Tutorial[]
  loading: boolean
  error: string | null
  hasAccess: (tutorialId: string) => boolean
  refetch: () => Promise<void>
}

/**
 * Hook for managing user purchases with direct Supabase queries
 * Replaces the API route approach with direct database access
 */
export function usePurchases(userId?: string): UsePurchasesReturn {
  const [purchases, setPurchases] = useState<Purchase[]>([])
  const [purchasedTutorials, setPurchasedTutorials] = useState<Tutorial[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPurchases = async () => {
    if (!userId) {
      console.log('🔍 usePurchases: No userId provided, clearing data')
      setPurchases([])
      setPurchasedTutorials([])
      setLoading(false)
      return
    }

    try {
      console.log('🔍 usePurchases: Fetching purchases for user:', userId)
      setLoading(true)
      setError(null)

      // Fetch user purchases with tutorial details in a single query
      const { data: purchaseData, error: purchaseError } = await supabase
        .from('purchases')
        .select(`
          *,
          tutorials (
            id,
            title,
            description,
            price,
            featured,
            difficulty,
            duration,
            cover_image,
            preview_video_url,
            created_at,
            updated_at,
            videos (
              id,
              title,
              description,
              duration,
              video_url,
              thumbnail_url,
              order_index,
              created_at,
              updated_at
            )
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'completed')
        .order('created_at', { ascending: false })

      if (purchaseError) {
        throw new Error(`Failed to fetch purchases: ${purchaseError.message}`)
      }

      console.log('🔍 usePurchases: Raw purchase data:', purchaseData)

      // Transform the data
      const transformedPurchases: Purchase[] = purchaseData.map(purchase => ({
        id: purchase.id,
        userId: purchase.user_id,
        tutorialId: purchase.tutorial_id,
        amount: purchase.amount,
        paypalOrderId: purchase.paypal_order_id,
        status: purchase.status,
        createdAt: new Date(purchase.created_at),
        updatedAt: new Date(purchase.updated_at)
      }))

      const transformedTutorials: Tutorial[] = purchaseData
        .filter(purchase => purchase.tutorials) // Filter out any null tutorials
        .map(purchase => {
          const tutorial = purchase.tutorials
          return {
            id: tutorial.id,
            title: tutorial.title,
            description: tutorial.description,
            price: tutorial.price,
            featured: tutorial.featured,
            difficulty: tutorial.difficulty,
            duration: tutorial.duration,
            coverImage: tutorial.cover_image,
            preview_video_url: tutorial.preview_video_url,
            createdAt: new Date(tutorial.created_at),
            updatedAt: new Date(tutorial.updated_at),
            videos: tutorial.videos?.map((video: any) => ({
              id: video.id,
              tutorialId: tutorial.id,
              title: video.title,
              description: video.description,
              duration: video.duration,
              videoUrl: video.video_url,
              thumbnailUrl: video.thumbnail_url,
              order: video.order_index,
              createdAt: new Date(video.created_at),
              updatedAt: new Date(video.updated_at)
            })) || []
          }
        })

      console.log('✅ usePurchases: Transformed purchases:', transformedPurchases)
      console.log('✅ usePurchases: Transformed tutorials:', transformedTutorials)

      setPurchases(transformedPurchases)
      setPurchasedTutorials(transformedTutorials)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch purchases'
      setError(errorMessage)
      console.error('❌ usePurchases error:', err)
      // Set empty arrays on error to prevent infinite loading
      setPurchases([])
      setPurchasedTutorials([])
    } finally {
      console.log('🔍 usePurchases: Loading complete')
      setLoading(false)
    }
  }

  // Check if user has access to a specific tutorial
  const hasAccess = (tutorialId: string): boolean => {
    return purchases.some(purchase => 
      purchase.tutorialId === tutorialId && purchase.status === 'completed'
    )
  }

  // Refetch purchases
  const refetch = async () => {
    await fetchPurchases()
  }

  useEffect(() => {
    fetchPurchases()
  }, [userId])

  return {
    purchases,
    purchasedTutorials,
    loading,
    error,
    hasAccess,
    refetch
  }
}

/**
 * Hook for checking access to a specific tutorial
 * Optimized for single tutorial access checks
 */
export function useTutorialAccess(tutorialId: string, userId?: string) {
  const [hasAccess, setHasAccess] = useState<boolean | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function checkAccess() {
      if (!userId || !tutorialId) {
        setHasAccess(false)
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)

        const { data, error: queryError } = await supabase
          .from('purchases')
          .select('id')
          .eq('user_id', userId)
          .eq('tutorial_id', tutorialId)
          .eq('status', 'completed')
          .single()

        if (queryError && queryError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
          throw queryError
        }

        setHasAccess(!!data)
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to check tutorial access'
        setError(errorMessage)
        console.error('useTutorialAccess error:', err)
        setHasAccess(false)
      } finally {
        setLoading(false)
      }
    }

    checkAccess()
  }, [userId, tutorialId])

  return { hasAccess, loading, error }
}

/**
 * Hook for creating a new purchase
 * Used after successful PayPal payment
 */
export function useCreatePurchase() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createPurchase = async (purchaseData: {
    userId: string
    tutorialId: string
    paypalOrderId: string
    amount: number
  }): Promise<Purchase | null> => {
    try {
      setLoading(true)
      setError(null)

      const { data, error: insertError } = await supabase
        .from('purchases')
        .insert({
          user_id: purchaseData.userId,
          tutorial_id: purchaseData.tutorialId,
          paypal_order_id: purchaseData.paypalOrderId,
          amount: purchaseData.amount,
          status: 'completed',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (insertError) {
        throw new Error(`Failed to create purchase: ${insertError.message}`)
      }

      return {
        id: data.id,
        userId: data.user_id,
        tutorialId: data.tutorial_id,
        amount: data.amount,
        paypalOrderId: data.paypal_order_id,
        status: data.status,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at)
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create purchase'
      setError(errorMessage)
      console.error('useCreatePurchase error:', err)
      return null
    } finally {
      setLoading(false)
    }
  }

  return { createPurchase, loading, error }
}
