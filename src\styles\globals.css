@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Crimson+Text:wght@400;600&family=JetBrains+Mono:wght@400;500&display=swap');

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-cream-50 text-navy-900 font-body;
    background-image:
      linear-gradient(135deg, rgba(248, 245, 235, 0.8) 0%, rgba(247, 232, 211, 0.6) 100%);
    line-height: 1.7;
    letter-spacing: 0.01em;
  }

  /* Classical typography hierarchy */
  h1, h2, h3, h4, h5, h6 {
    @apply font-display text-navy-900;
    line-height: 1.3;
    letter-spacing: -0.02em;
  }

  h1 {
    @apply text-4xl md:text-6xl font-semibold;
  }

  h2 {
    @apply text-3xl md:text-4xl font-medium;
  }

  h3 {
    @apply text-2xl md:text-3xl font-medium;
  }

  p {
    @apply text-navy-800 leading-relaxed;
  }
}

@layer components {
  /* Classical button styles */
  .btn-primary {
    @apply bg-burgundy-700 hover:bg-burgundy-800 text-cream-50 font-medium py-3 px-8
           border border-burgundy-600 shadow-md hover:shadow-lg
           transition-all duration-300 font-display tracking-wide;
    border-radius: 4px;
  }

  .btn-secondary {
    @apply bg-navy-700 hover:bg-navy-800 text-cream-50 font-medium py-3 px-8
           border border-navy-600 shadow-md hover:shadow-lg
           transition-all duration-300 font-display tracking-wide;
    border-radius: 4px;
  }

  .btn-outline {
    @apply border-2 border-gold-600 text-gold-700 hover:bg-gold-600 hover:text-cream-50
           font-medium py-3 px-8 shadow-sm hover:shadow-md
           transition-all duration-300 font-display tracking-wide;
    border-radius: 4px;
  }

  /* Classical card design */
  .card {
    @apply bg-cream-100 border-2 border-navy-200 shadow-lg p-8;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(16, 42, 67, 0.15), 0 2px 4px rgba(16, 42, 67, 0.1);
  }

  /* Classical form inputs */
  .input-field {
    @apply bg-cream-50 border-2 border-navy-300 text-navy-900 px-4 py-3
           focus:outline-none focus:border-gold-500 focus:ring-2 focus:ring-gold-200
           transition-all duration-200 font-body;
    border-radius: 4px;
  }

  /* Classical text styling */
  .text-accent {
    @apply text-burgundy-700 font-display font-medium;
  }

  /* Classical hero section */
  .hero-section {
    @apply min-h-screen flex items-center justify-center relative;
    background: linear-gradient(135deg, #fefdfb 0%, #f7e8d3 100%);
  }

  /* Classical section spacing using golden ratio */
  .section-padding {
    @apply py-16 px-4 sm:py-20 sm:px-6 lg:py-24 lg:px-12;
  }

  .container-max {
    @apply max-w-6xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Responsive typography */
  @media (max-width: 768px) {
    h1 {
      @apply text-3xl;
    }

    h2 {
      @apply text-2xl;
    }

    .ornamental-divider::before {
      font-size: 1.5rem;
      letter-spacing: 0.5rem;
    }

    .classical-border::before {
      width: 60px;
    }
  }

  /* Classical decorative elements */
  .classical-border {
    position: relative;
    padding-top: 1rem;
  }

  .classical-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #b83c3c, #d97706, #b83c3c, transparent);
    border-radius: 2px;
  }

  /* Classical corner flourishes */
  .corner-flourish {
    position: relative;
  }

  .corner-flourish::before,
  .corner-flourish::after {
    content: '❦';
    position: absolute;
    color: #d97706;
    font-size: 1.5rem;
    opacity: 0.6;
  }

  .corner-flourish::before {
    top: -0.5rem;
    left: -0.5rem;
  }

  .corner-flourish::after {
    bottom: -0.5rem;
    right: -0.5rem;
    transform: rotate(180deg);
  }

  /* Classical section divider */
  .section-divider {
    text-align: center;
    margin: 3rem 0;
    position: relative;
  }

  .section-divider::before {
    content: '◆ ◆ ◆';
    color: #d97706;
    font-size: 1rem;
    letter-spacing: 1rem;
    background: #fefdfb;
    padding: 0 2rem;
    position: relative;
    z-index: 1;
  }

  .section-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #d97706, transparent);
    z-index: 0;
  }
}

@layer utilities {
  /* Classical text shadow */
  .text-shadow {
    text-shadow: 0 1px 3px rgba(16, 42, 67, 0.2);
  }

  /* Classical paper texture effect */
  .paper-texture {
    background-image:
      radial-gradient(circle at 1px 1px, rgba(16, 42, 67, 0.05) 1px, transparent 0);
    background-size: 20px 20px;
  }

  /* Elegant hover effect */
  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-xl;
  }

  /* Classical ornamental divider */
  .ornamental-divider {
    position: relative;
    text-align: center;
    margin: 2rem 0;
  }

  .ornamental-divider::before {
    content: '❦';
    @apply text-gold-600 text-2xl;
    background: #fefdfb;
    padding: 0 1rem;
  }

  .ornamental-divider::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #d97706, transparent);
    z-index: -1;
  }

  /* Form Styles */
  .input-field {
    @apply w-full px-4 py-3 border border-navy-300 rounded-lg font-body text-navy-900 bg-cream-50 focus:outline-none focus:ring-2 focus:ring-burgundy-500 focus:border-transparent transition-all duration-200;
  }

  .input-field:focus {
    @apply bg-white shadow-md;
  }

  /* Line Clamp Utility */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
