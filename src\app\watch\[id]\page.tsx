'use client'

import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { Tutorial, Video } from '@/types'
import { notFound } from 'next/navigation'
import { useAuth, withAuth, useTutorialAccess } from '@/contexts/AuthContext'
import { tutorialModel } from '@/lib/database'



interface WatchPageProps {
  params: {
    id: string
  }
}

function WatchPage({ params }: WatchPageProps) {
  console.log('🔍 WatchPage: Component rendering for tutorial ID:', params.id)
  const { user, loading } = useAuth()
  const { hasAccess, loading: accessLoading } = useTutorialAccess(params.id)
  const [tutorial, setTutorial] = useState<Tutorial | null>(null)
  const [currentVideo, setCurrentVideo] = useState<Video | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadTutorial = async () => {
      try {
        console.log('🔍 WatchPage: Loading tutorial data for ID:', params.id)
        const tutorialData = await tutorialModel.getById(params.id)
        if (tutorialData) {
          console.log('✅ WatchPage: Tutorial loaded:', tutorialData.title)
          setTutorial(tutorialData)
          // Set default video to first one
          if (tutorialData.videos && tutorialData.videos.length > 0) {
            setCurrentVideo(tutorialData.videos[0])
          }
        } else {
          console.log('❌ WatchPage: Tutorial not found')
        }
      } catch (error) {
        console.error('❌ WatchPage: Error loading tutorial:', error)
      } finally {
        setIsLoading(false)
      }
    }

    if (!loading && !accessLoading) {
      console.log('🔍 WatchPage: Auth loaded, user:', user, 'hasAccess:', hasAccess)
      if (hasAccess) {
        loadTutorial()
      } else {
        console.log('❌ WatchPage: User does not have access to this tutorial')
        setIsLoading(false)
      }
    }
  }, [params.id, loading, accessLoading, user, hasAccess])

  if (isLoading || loading || accessLoading) {
    return (
      <div className="min-h-screen bg-cream-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse">
            <svg className="w-6 h-6 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-navy-600 font-body">Loading tutorial...</p>
        </div>
      </div>
    )
  }

  if (!tutorial || !hasAccess) {
    console.log('❌ WatchPage: Access denied or tutorial not found')
    return (
      <div className="min-h-screen bg-cream-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-display font-bold text-navy-900 mb-2">Access Denied</h2>
          <p className="text-navy-600 font-body mb-4">You don't have access to this tutorial.</p>
          <Link href="/library" className="inline-block bg-burgundy-600 text-cream-50 px-6 py-2 rounded-lg font-display hover:bg-burgundy-700 transition-colors">
            Back to Library
          </Link>
        </div>
      </div>
    )
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <div className="min-h-screen bg-navy-900 text-cream-50">
      {/* Minimal Navigation for Watch Mode */}
      <nav className="fixed top-0 w-full z-50 bg-navy-900/95 backdrop-blur-sm border-b border-navy-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <Link href="/library" className="flex items-center text-cream-100 hover:text-gold-400 transition-colors">
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              <span className="font-display">Back to Library</span>
            </Link>
            
            <div className="text-center">
              <h1 className="text-lg font-display font-medium text-cream-100">
                {tutorial.title}
              </h1>
            </div>

            <div className="w-20"></div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="pt-20">
        <div className="max-w-7xl mx-auto flex gap-6 p-6">
          {/* Video Player Section */}
          <div className="flex-1">
            {/* Video Container */}
            <div className="aspect-video bg-black rounded-lg overflow-hidden mb-6 shadow-2xl">
              <div className="w-full h-full bg-gradient-to-br from-navy-800 to-navy-900 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-20 h-20 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-6 hover:bg-burgundy-800 transition-colors cursor-pointer">
                    <svg className="w-8 h-8 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="text-cream-200 font-body text-lg mb-2">
                    {currentVideo?.title || 'Select a video to start learning'}
                  </p>
                  <p className="text-cream-400 font-body text-sm">
                    High-quality video player will be integrated here
                  </p>
                </div>
              </div>
            </div>

            {/* Video Info */}
            {currentVideo && (
              <div className="mb-8">
                <h2 className="text-2xl md:text-3xl font-display font-medium mb-3 text-cream-100">
                  {currentVideo.title}
                </h2>
                <p className="text-cream-300 font-body leading-relaxed mb-4">
                  {currentVideo.description}
                </p>
                <div className="flex items-center text-cream-400 font-body text-sm">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Duration: {formatDuration(currentVideo.duration)}
                </div>
              </div>
            )}

            {/* Video Controls */}
            <div className="flex items-center justify-between mb-8 p-4 bg-navy-800 rounded-lg">
              <button 
                onClick={() => {
                  const currentIndex = tutorial.videos.findIndex(v => v.id === currentVideo?.id)
                  if (currentIndex > 0) {
                    setCurrentVideo(tutorial.videos[currentIndex - 1])
                  }
                }}
                disabled={!currentVideo || tutorial.videos.findIndex(v => v.id === currentVideo.id) === 0}
                className="flex items-center text-cream-200 hover:text-cream-100 disabled:text-navy-500 disabled:cursor-not-allowed transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Previous
              </button>

              <div className="text-center">
                <div className="text-cream-300 font-body text-sm">
                  {currentVideo ? tutorial.videos.findIndex(v => v.id === currentVideo.id) + 1 : 0} of {tutorial.videos.length}
                </div>
              </div>

              <button 
                onClick={() => {
                  const currentIndex = tutorial.videos.findIndex(v => v.id === currentVideo?.id)
                  if (currentIndex < tutorial.videos.length - 1) {
                    setCurrentVideo(tutorial.videos[currentIndex + 1])
                  }
                }}
                disabled={!currentVideo || tutorial.videos.findIndex(v => v.id === currentVideo.id) === tutorial.videos.length - 1}
                className="flex items-center text-cream-200 hover:text-cream-100 disabled:text-navy-500 disabled:cursor-not-allowed transition-colors"
              >
                Next
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>

          {/* Playlist Sidebar */}
          <div className="w-80 bg-navy-800 border border-navy-700 rounded-lg p-6 overflow-y-auto" style={{maxHeight: 'calc(100vh - 140px)'}}>
          <div className="mb-6">
            <h3 className="text-xl font-display font-medium text-cream-100 mb-2">
              Course Content
            </h3>
            <p className="text-cream-400 font-body text-sm">
              {tutorial.videos.length} lessons • {Math.floor(tutorial.videos.reduce((total, video) => total + video.duration, 0) / 60)} minutes
            </p>
          </div>

          <div className="space-y-2">
            {tutorial.videos.map((video, index) => (
              <button
                key={video.id}
                onClick={() => setCurrentVideo(video)}
                className={`w-full text-left p-4 rounded-lg transition-all duration-200 ${
                  currentVideo?.id === video.id
                    ? 'bg-burgundy-700 text-cream-50'
                    : 'bg-navy-700 text-cream-200 hover:bg-navy-600'
                }`}
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0 mr-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-display font-medium ${
                      currentVideo?.id === video.id
                        ? 'bg-cream-100 text-burgundy-700'
                        : 'bg-navy-600 text-cream-300'
                    }`}>
                      {index + 1}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-display font-medium mb-1 text-sm leading-tight">
                      {video.title}
                    </h4>
                    <p className="font-body text-xs opacity-75 mb-2 line-clamp-2">
                      {video.description}
                    </p>
                    <div className="flex items-center text-xs opacity-60">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {formatDuration(video.duration)}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>

          {/* Progress Summary */}
          <div className="mt-8 p-4 bg-navy-700 rounded-lg">
            <h4 className="font-display font-medium text-cream-100 mb-3">
              Your Progress
            </h4>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-cream-300">Completed</span>
                <span className="text-cream-200">2 of {tutorial.videos.length}</span>
              </div>
              <div className="w-full bg-navy-600 rounded-full h-2">
                <div 
                  className="bg-burgundy-600 h-2 rounded-full transition-all duration-300" 
                  style={{width: `${(2 / tutorial.videos.length) * 100}%`}}
                ></div>
              </div>
              <div className="text-xs text-cream-400 text-center">
                Keep up the great work!
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Export the component wrapped with authentication protection
export default withAuth(WatchPage)
