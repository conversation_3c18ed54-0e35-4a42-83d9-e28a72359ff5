import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { PortfolioItem } from '@/types'
import { PortfolioDataAccess } from '@/lib/data-access'

async function getPortfolioItems(): Promise<PortfolioItem[]> {
  try {
    return await PortfolioDataAccess.getAll()
  } catch (error) {
    console.error('Error fetching portfolio items:', error)
    return []
  }
}
export default async function PortfolioPage() {
  const portfolioItems = await getPortfolioItems()
  return (
    <div className="min-h-screen bg-cream-50 text-navy-900">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-6xl text-burgundy-700 rotate-12">❦</div>
          <div className="absolute top-20 right-20 text-4xl text-gold-600 -rotate-12">◆</div>
          <div className="absolute bottom-20 left-20 text-5xl text-navy-600 rotate-45">✦</div>
          <div className="absolute bottom-10 right-10 text-3xl text-burgundy-600 -rotate-45">❦</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="classical-border mb-12">
            <h1 className="text-5xl md:text-6xl font-display font-semibold mb-6 text-navy-900 text-shadow">
              Portfolio
            </h1>
            <p className="text-xl md:text-2xl text-navy-700 max-w-3xl mx-auto font-body leading-relaxed">
              Witness the artistry and precision of classical magic through carefully curated performances 
              that showcase the timeless beauty of the impossible.
            </p>
          </div>
        </div>
      </section>

      {/* Portfolio Grid */}
      <section className="section-padding bg-cream-100 paper-texture">
        <div className="container-max">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
            {portfolioItems.map((item) => (
              <Link 
                key={item.id} 
                href={`/portfolio/${item.id}`}
                className="group block"
              >
                <div className="card hover-lift group-hover:shadow-2xl transition-all duration-300">
                  {/* Media Container */}
                  <div className="relative mb-6 overflow-hidden rounded-lg bg-navy-100">
                    <div className="aspect-video bg-gradient-to-br from-navy-200 to-burgundy-100 flex items-center justify-center">
                      {item.mediaType === 'video' ? (
                        <div className="text-center">
                          <div className="w-16 h-16 bg-burgundy-700 rounded-full flex items-center justify-center mb-3 mx-auto group-hover:bg-burgundy-800 transition-colors">
                            <svg className="w-6 h-6 text-cream-50 ml-1" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M8 5v14l11-7z"/>
                            </svg>
                          </div>
                          <p className="text-navy-600 font-display text-sm">Video Performance</p>
                        </div>
                      ) : (
                        <div className="text-center">
                          <div className="w-16 h-16 bg-gold-600 rounded-full flex items-center justify-center mb-3 mx-auto group-hover:bg-gold-700 transition-colors">
                            <svg className="w-6 h-6 text-cream-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                          <p className="text-navy-600 font-display text-sm">Image Gallery</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Content */}
                  <div>
                    <h3 className="text-2xl font-display font-medium mb-3 text-burgundy-700 group-hover:text-burgundy-800 transition-colors">
                      {item.title}
                    </h3>
                    <p className="text-navy-700 font-body leading-relaxed mb-4">
                      {item.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-navy-500 font-body">
                        {item.createdAt.toLocaleDateString('en-US', { 
                          year: 'numeric', 
                          month: 'long', 
                          day: 'numeric' 
                        })}
                      </span>
                      <span className="text-burgundy-600 font-display text-sm group-hover:text-burgundy-800 transition-colors">
                        View Details →
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-padding">
        <div className="container-max text-center">
          <div className="ornamental-divider mb-8"></div>
          <h2 className="text-3xl md:text-4xl font-display font-medium mb-6 text-navy-900">
            Ready to Learn These Techniques?
          </h2>
          <p className="text-lg text-navy-700 mb-8 max-w-2xl mx-auto font-body leading-relaxed">
            Discover the secrets behind these mesmerizing performances through our exclusive tutorial collection.
          </p>
          <Link href="/tutorials" className="btn-primary text-lg">
            Explore Tutorials
          </Link>
        </div>
      </section>

      {/* Classical Footer */}
      <footer className="section-padding bg-navy-900 border-t-4 border-gold-600 relative">
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-4xl text-gold-400">❦</div>
          <div className="absolute top-10 right-10 text-4xl text-gold-400 rotate-180">❦</div>
          <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-6xl text-cream-200">◆</div>
        </div>

        <div className="container-max text-center relative z-10">
          <div className="section-divider mb-12"></div>
          <div className="flex items-center justify-center mb-6">
            <span className="text-gold-400 text-2xl mr-3">❦</span>
            <div className="text-3xl font-display font-semibold text-cream-100 tracking-wide">
              Magic Academy
            </div>
            <span className="text-gold-400 text-2xl ml-3 rotate-180">❦</span>
          </div>
          <p className="text-navy-300 font-body text-lg mb-8 max-w-2xl mx-auto italic">
            "Preserving the classical traditions of magical artistry for future generations,
            where timeless elegance meets the wonder of the impossible."
          </p>
          <div className="flex flex-wrap justify-center gap-4 sm:gap-8 mb-8">
            <Link href="/" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Home</Link>
            <Link href="/portfolio" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Portfolio</Link>
            <Link href="/blog" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Blog</Link>
            <Link href="/tutorials" className="text-navy-400 hover:text-gold-400 transition-colors font-display">Tutorials</Link>
          </div>
          <div className="border-t border-navy-700 pt-8">
            <p className="text-navy-400 font-body">
              © 2025 Magic Academy. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
