'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { SupabaseAuthService } from '@/lib/auth-supabase'
import type { AppUser, Tutorial } from '@/types'
import type { Session } from '@supabase/supabase-js'

interface AuthContextType {
  user: AppUser | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, name?: string) => Promise<void>
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AppUser | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize auth state
  useEffect(() => {
    let mounted = true

    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (!mounted) return

      setSession(session)
      setIsInitialized(true)

      if (session?.user) {
        // 直接从 session 创建用户，避免额外的数据库查询导致的超时问题
        const sessionUser = {
          id: session.user.id,
          email: session.user.email!,
          name: session.user.user_metadata?.name || session.user.email!.split('@')[0],
          createdAt: new Date(session.user.created_at),
          updatedAt: new Date(session.user.updated_at || session.user.created_at)
        }
        setUser(sessionUser)
        setLoading(false)
      } else {
        setLoading(false)
      }
    }).catch((error) => {
      if (!mounted) return
      console.error('AuthContext: Error getting initial session:', error)
      setIsInitialized(true)
      setLoading(false)
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mounted) return

      // Skip INITIAL_SESSION event if we've already initialized
      if (event === 'INITIAL_SESSION' && isInitialized) {
        return
      }

      setSession(session)

      if (session?.user) {
        // Only load profile for non-initial events or if not yet initialized
        if (event !== 'INITIAL_SESSION' || !isInitialized) {
          // 直接从 session 创建用户，避免数据库查询超时
          const sessionUser = {
            id: session.user.id,
            email: session.user.email!,
            name: session.user.user_metadata?.name || session.user.email!.split('@')[0],
            createdAt: new Date(session.user.created_at),
            updatedAt: new Date(session.user.updated_at || session.user.created_at)
          }
          setUser(sessionUser)
          setLoading(false)
        }
      } else {
        setUser(null)
        setLoading(false)
      }
    })

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, []) // Remove isInitialized dependency to prevent re-initialization



  const signIn = async (email: string, password: string) => {
    setLoading(true)
    try {
      await SupabaseAuthService.signIn(email, password)
      // User state will be updated by the auth state change listener
    } catch (error) {
      setLoading(false)
      throw error
    }
  }

  const signUp = async (email: string, password: string, name?: string) => {
    setLoading(true)
    try {
      await SupabaseAuthService.signUp(email, password, name)
      // User state will be updated by the auth state change listener
    } catch (error) {
      setLoading(false)
      throw error
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      await SupabaseAuthService.signOut()
      // User state will be updated by the auth state change listener
    } catch (error) {
      setLoading(false)
      throw error
    }
  }

  const refreshUser = async () => {
    if (session?.user) {
      // 直接从 session 重新创建用户
      const sessionUser = {
        id: session.user.id,
        email: session.user.email!,
        name: session.user.user_metadata?.name || session.user.email!.split('@')[0],
        createdAt: new Date(session.user.created_at),
        updatedAt: new Date(session.user.updated_at || session.user.created_at)
      }
      setUser(sessionUser)
    }
  }

  const value: AuthContextType = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    refreshUser
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Higher-order component for protecting routes
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { user, loading } = useAuth()
    const [timeoutReached, setTimeoutReached] = useState(false)
    const [startTime] = useState(Date.now())

    // Add timeout to prevent infinite loading
    useEffect(() => {
      const timeout = setTimeout(() => {
        if (loading) {
          console.log('⚠️ withAuth: Loading timeout reached after 30 seconds')
          setTimeoutReached(true)
        }
      }, 30000) // 30 second timeout - increased for debugging

      return () => clearTimeout(timeout)
    }, [loading])

    const elapsedTime = Math.round((Date.now() - startTime) / 1000)

    if (loading && !timeoutReached) {
      return (
        <div className="min-h-screen bg-cream-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-burgundy-600 mx-auto mb-4"></div>
            <p className="text-navy-700 font-body">Loading... ({elapsedTime}s)</p>
            {elapsedTime > 10 && (
              <p className="text-navy-500 font-body text-sm mt-2">
                Taking longer than expected... (Debug mode: check console)
              </p>
            )}
          </div>
        </div>
      )
    }

    if (!user || timeoutReached) {
      // Redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
      return null
    }


    return <Component {...props} />
  }
}

// Hook for checking if user has purchased a tutorial
export function useTutorialAccess(tutorialId: string) {
  const { user } = useAuth()
  const [hasAccess, setHasAccess] = useState<boolean | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function checkAccess() {
      console.log('🔍 useTutorialAccess: Checking access for tutorial:', tutorialId, 'user:', user?.id)

      if (!user || !tutorialId) {
        console.log('❌ useTutorialAccess: No user or tutorial ID')
        setHasAccess(false)
        setLoading(false)
        return
      }

      try {
        // Direct Supabase query instead of using PurchaseModel
        const { data, error } = await supabase
          .from('purchases')
          .select('id')
          .eq('user_id', user.id)
          .eq('tutorial_id', tutorialId)
          .eq('status', 'completed')
          .single()

        if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
          throw error
        }

        const purchased = !!data
        console.log('✅ useTutorialAccess: Access check result:', purchased)
        setHasAccess(purchased)
      } catch (error) {
        console.error('❌ useTutorialAccess: Error checking tutorial access:', error)
        setHasAccess(false)
      } finally {
        setLoading(false)
      }
    }

    checkAccess()
  }, [user, tutorialId])

  return { hasAccess, loading }
}

// Hook for getting user's purchased tutorials
export function usePurchasedTutorials() {
  const { user } = useAuth()
  const [purchasedTutorials, setPurchasedTutorials] = useState<Tutorial[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchPurchasedTutorials() {
      console.log('🔍 usePurchasedTutorials: Fetching tutorials for user:', user)

      if (!user) {
        console.log('❌ usePurchasedTutorials: No user, setting empty tutorials')
        setPurchasedTutorials([])
        setLoading(false)
        return
      }

      try {
        console.log('🔍 usePurchasedTutorials: Fetching purchases with tutorial details...')

        // Direct Supabase query with join to get purchases and tutorial details
        const { data: purchaseData, error } = await supabase
          .from('purchases')
          .select(`
            *,
            tutorials (
              id,
              title,
              description,
              price,
              featured,
              difficulty,
              duration,
              cover_image,
              preview_video_url,
              created_at,
              updated_at,
              videos (
                id,
                title,
                description,
                duration,
                video_url,
                thumbnail_url,
                order_index,
                created_at,
                updated_at
              )
            )
          `)
          .eq('user_id', user.id)
          .eq('status', 'completed')
          .order('created_at', { ascending: false })

        if (error) {
          throw error
        }

        // Transform the data to Tutorial format
        const tutorials: Tutorial[] = purchaseData
          .filter(purchase => purchase.tutorials) // Filter out any null tutorials
          .map(purchase => {
            const tutorial = purchase.tutorials
            return {
              id: tutorial.id,
              title: tutorial.title,
              description: tutorial.description,
              price: tutorial.price,
              featured: tutorial.featured || false,
              difficulty: tutorial.difficulty,
              duration: tutorial.duration,
              coverImage: tutorial.cover_image,
              previewVideoUrl: tutorial.preview_video_url,
              createdAt: new Date(tutorial.created_at),
              updatedAt: new Date(tutorial.updated_at),
              videos: tutorial.videos?.map((video: any) => ({
                id: video.id,
                tutorialId: tutorial.id,
                title: video.title,
                description: video.description,
                duration: video.duration,
                videoUrl: video.video_url,
                thumbnailUrl: video.thumbnail_url,
                order: video.order_index,
                createdAt: new Date(video.created_at),
                updatedAt: new Date(video.updated_at)
              })) || []
            }
          })

        console.log('✅ usePurchasedTutorials: Valid tutorials:', tutorials)
        setPurchasedTutorials(tutorials)
      } catch (error) {
        console.error('❌ usePurchasedTutorials: Error fetching purchased tutorials:', error)
        setPurchasedTutorials([])
      } finally {
        console.log('🔍 usePurchasedTutorials: Loading complete')
        setLoading(false)
      }
    }

    fetchPurchasedTutorials()
  }, [user])

  return { purchasedTutorials, loading }
}
