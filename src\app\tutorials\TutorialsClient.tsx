'use client'

import Navigation from '@/components/Navigation'
import Link from 'next/link'
import { Tutorial } from '@/types'
import { useAuth, usePurchasedTutorials } from '@/contexts/AuthContext'

interface TutorialsClientProps {
  tutorials: Tutorial[]
}

export default function TutorialsClient({ tutorials }: TutorialsClientProps) {
  const { user } = useAuth()
  const { purchasedTutorials, loading: purchasedLoading } = usePurchasedTutorials()

  // Helper function to check if a tutorial is purchased
  const isPurchased = (tutorialId: string) => {
    return purchasedTutorials.some(purchased => purchased.id === tutorialId)
  }

  return (
    <div className="min-h-screen bg-cream-50 text-navy-900">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 text-6xl text-burgundy-700 rotate-12">❦</div>
          <div className="absolute top-20 right-20 text-4xl text-gold-600 -rotate-12">◆</div>
          <div className="absolute bottom-20 left-20 text-5xl text-navy-600 rotate-45">✦</div>
          <div className="absolute bottom-10 right-10 text-3xl text-burgundy-600 -rotate-45">❦</div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 text-navy-900">
              Master the Art of
              <span className="block text-burgundy-700">Magic</span>
            </h1>
            <p className="text-xl md:text-2xl text-navy-700 mb-8 leading-relaxed">
              Learn from centuries of magical wisdom with our comprehensive video tutorials. 
              Each lesson is crafted to transform you from student to master.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="#tutorials" 
                className="bg-burgundy-700 text-cream-50 px-8 py-4 rounded-lg font-semibold hover:bg-burgundy-800 transition-colors shadow-lg"
              >
                Browse Tutorials
              </Link>
              <Link 
                href="/about" 
                className="border-2 border-navy-700 text-navy-700 px-8 py-4 rounded-lg font-semibold hover:bg-navy-700 hover:text-cream-50 transition-colors"
              >
                Learn More
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Tutorials Grid */}
      <section id="tutorials" className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-navy-900 mb-4">
              Premium Video Tutorials
            </h2>
            <p className="text-xl text-navy-600 max-w-3xl mx-auto">
              Each tutorial includes multiple high-definition video lessons, detailed explanations, 
              and practice exercises designed to perfect your technique.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {tutorials.map((tutorial) => {
              const purchased = isPurchased(tutorial.id)

              return (
                <Link
                  key={tutorial.id}
                  href={`/tutorials/${tutorial.id}`}
                  className="group block"
                >
                  <div className="bg-cream-50 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-[1.02] relative">
                    {/* Purchased Badge */}
                    {purchased && (
                      <div className="absolute top-4 right-4 z-10">
                        <div className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center shadow-lg">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                          Purchased
                        </div>
                      </div>
                    )}

                    <div className="aspect-video bg-navy-100 relative overflow-hidden">
                      <img
                        src={tutorial.coverImage}
                        alt={tutorial.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-navy-900 bg-opacity-20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                        <div className="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
                          <div className="w-0 h-0 border-l-[12px] border-l-burgundy-700 border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1"></div>
                        </div>
                      </div>
                    </div>

                    <div className="p-6">
                      <h3 className="text-xl font-bold text-navy-900 mb-3 group-hover:text-burgundy-700 transition-colors">
                        {tutorial.title}
                      </h3>
                      <p className="text-navy-600 mb-4 line-clamp-3">
                        {tutorial.description}
                      </p>

                      <div className="flex items-center justify-between">
                        <span className="text-2xl font-bold text-burgundy-700">
                          ${tutorial.price}
                        </span>
                        <div className="flex items-center gap-2">
                          {tutorial.difficulty && (
                            <span className="px-3 py-1 bg-gold-100 text-gold-800 rounded-full text-sm font-medium capitalize">
                              {tutorial.difficulty}
                            </span>
                          )}
                          <span className="text-burgundy-600 font-display text-sm group-hover:text-burgundy-800 transition-colors">
                            {purchased ? 'Watch →' : 'View Details →'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-navy-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-navy-900 mb-4">
              Why Choose Our Tutorials?
            </h2>
            <p className="text-xl text-navy-600 max-w-3xl mx-auto">
              Our tutorials are designed by master magicians with decades of performance experience.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl text-cream-50">🎭</span>
              </div>
              <h3 className="text-xl font-bold text-navy-900 mb-4">Professional Quality</h3>
              <p className="text-navy-600">
                High-definition video production with multiple camera angles and crystal-clear audio.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl text-cream-50">📚</span>
              </div>
              <h3 className="text-xl font-bold text-navy-900 mb-4">Comprehensive Learning</h3>
              <p className="text-navy-600">
                Each tutorial includes theory, practice exercises, and performance tips.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl text-cream-50">⭐</span>
              </div>
              <h3 className="text-xl font-bold text-navy-900 mb-4">Expert Instruction</h3>
              <p className="text-navy-600">
                Learn from master magicians with decades of professional performance experience.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-navy-900 text-cream-50 py-12">
        <div className="container mx-auto px-6">
          <div className="text-center">
            <h3 className="text-2xl font-bold mb-4">Magic Academy</h3>
            <p className="text-cream-200 mb-6">
              Preserving the art of magic through education and wonder.
            </p>
            <p className="text-cream-300 text-sm">
              © 2025 Magic Academy. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
