import { TutorialDataAccess } from '@/lib/data-access'
import TutorialsClient from './TutorialsClient'

async function getTutorials() {
  try {
    return await TutorialDataAccess.getAll()
  } catch (error) {
    console.error('Error fetching tutorials:', error)
    return []
  }
}

export default async function TutorialsPage() {
  const tutorials = await getTutorials()

  return <TutorialsClient tutorials={tutorials} />
}
