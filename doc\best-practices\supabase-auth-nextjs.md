# Supabase 认证在 Next.js 中的最佳实践

## 概述

本文档总结了在 Next.js 应用中使用 Supabase 认证的最佳实践，特别是如何避免常见的页面刷新和状态管理问题。

## 核心原则

### 1. 分离认证和数据加载

**认证阶段**：只关注用户身份验证，使用 session 中的基本信息
**数据加载阶段**：在认证完成后异步加载额外的用户数据

```typescript
// ✅ 好的做法
const initializeAuth = async () => {
  const { data: { session } } = await supabase.auth.getSession()
  
  if (session?.user) {
    // 立即设置基本用户信息
    setUser({
      id: session.user.id,
      email: session.user.email!,
      name: session.user.user_metadata?.name || session.user.email!.split('@')[0],
      createdAt: new Date(session.user.created_at),
      updatedAt: new Date(session.user.updated_at)
    })
    setLoading(false)
    
    // 异步加载额外数据
    loadUserProfile(session.user.id)
  }
}

// ❌ 避免的做法
const initializeAuth = async () => {
  const { data: { session } } = await supabase.auth.getSession()
  
  if (session?.user) {
    // 阻塞认证流程等待数据库查询
    const profile = await fetchUserProfile(session.user.id) // 可能超时
    setUser(profile)
    setLoading(false)
  }
}
```

### 2. 简化 useEffect 依赖

避免因状态变化导致的重复初始化：

```typescript
// ✅ 好的做法
useEffect(() => {
  initializeAuth()
}, []) // 只在组件挂载时执行

// ❌ 避免的做法
useEffect(() => {
  initializeAuth()
}, [isInitialized, user, session]) // 可能导致无限循环
```

### 3. 优雅的错误处理

```typescript
const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const initAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession()
        
        if (error) throw error
        
        if (session?.user) {
          setUser(createUserFromSession(session.user))
        }
      } catch (err) {
        setError(err.message)
        console.error('Auth initialization failed:', err)
      } finally {
        setLoading(false)
      }
    }

    initAuth()
  }, [])

  // 错误状态处理
  if (error) {
    return <ErrorBoundary error={error} />
  }

  return (
    <AuthContext.Provider value={{ user, loading, error }}>
      {children}
    </AuthContext.Provider>
  )
}
```

## 认证状态管理模式

### 1. 基础认证 Context

```typescript
interface AuthContextType {
  user: AppUser | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AppUser | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  // 从 session 创建用户对象的辅助函数
  const createUserFromSession = (authUser: User): AppUser => ({
    id: authUser.id,
    email: authUser.email!,
    name: authUser.user_metadata?.name || authUser.email!.split('@')[0],
    createdAt: new Date(authUser.created_at),
    updatedAt: new Date(authUser.updated_at)
  })

  useEffect(() => {
    let mounted = true

    // 初始化认证状态
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (!mounted) return

      setSession(session)
      if (session?.user) {
        setUser(createUserFromSession(session.user))
      }
      setLoading(false)
    })

    // 监听认证状态变化
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        if (!mounted) return

        setSession(session)
        if (session?.user) {
          setUser(createUserFromSession(session.user))
        } else {
          setUser(null)
        }
        setLoading(false)
      }
    )

    return () => {
      mounted = false
      subscription.unsubscribe()
    }
  }, [])

  return (
    <AuthContext.Provider value={{ user, session, loading, signIn, signOut, refreshUser }}>
      {children}
    </AuthContext.Provider>
  )
}
```

### 2. 路由保护 HOC

```typescript
export function withAuth<P extends object>(Component: React.ComponentType<P>) {
  return function AuthenticatedComponent(props: P) {
    const { user, loading } = useAuth()
    const [timeoutReached, setTimeoutReached] = useState(false)

    // 防止无限 loading
    useEffect(() => {
      const timeout = setTimeout(() => {
        if (loading) {
          setTimeoutReached(true)
        }
      }, 15000)

      return () => clearTimeout(timeout)
    }, [loading])

    if (loading && !timeoutReached) {
      return <LoadingSpinner />
    }

    if (!user || timeoutReached) {
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
      return null
    }

    return <Component {...props} />
  }
}
```

## 性能优化策略

### 1. 延迟加载用户数据

```typescript
// 在认证完成后异步加载额外数据
useEffect(() => {
  if (user && !user.profileLoaded) {
    loadUserProfile(user.id).then(profile => {
      setUser(prev => ({ ...prev, ...profile, profileLoaded: true }))
    })
  }
}, [user])
```

### 2. 缓存策略

```typescript
// 使用 localStorage 缓存基本用户信息
const cacheUser = (user: AppUser) => {
  localStorage.setItem('user_cache', JSON.stringify(user))
}

const getCachedUser = (): AppUser | null => {
  try {
    const cached = localStorage.getItem('user_cache')
    return cached ? JSON.parse(cached) : null
  } catch {
    return null
  }
}
```

### 3. 减少重新渲染

```typescript
// 使用 useMemo 优化 context value
const contextValue = useMemo(() => ({
  user,
  session,
  loading,
  signIn,
  signOut,
  refreshUser
}), [user, session, loading])
```

## 常见问题和解决方案

### 1. 页面刷新时无限 Loading

**原因**：复杂的用户数据加载逻辑阻塞了认证流程
**解决**：直接使用 session 数据，异步加载额外信息

### 2. 认证状态不同步

**原因**：多个组件独立管理认证状态
**解决**：使用统一的 AuthContext，避免重复的认证逻辑

### 3. 内存泄漏

**原因**：组件卸载后仍有异步操作更新状态
**解决**：使用 cleanup 函数和 mounted 标志

```typescript
useEffect(() => {
  let mounted = true

  const loadData = async () => {
    const data = await fetchData()
    if (mounted) {
      setData(data)
    }
  }

  loadData()

  return () => {
    mounted = false
  }
}, [])
```

## 调试技巧

1. **分阶段日志**：在关键节点添加日志
2. **状态监控**：使用 React DevTools 监控状态变化
3. **网络监控**：检查 Supabase 请求的响应时间
4. **错误边界**：使用 Error Boundary 捕获认证错误

## 总结

成功的 Supabase 认证实现应该：
- 快速完成基础认证
- 异步加载额外数据
- 优雅处理错误和超时
- 避免不必要的重新渲染
- 提供良好的用户体验
